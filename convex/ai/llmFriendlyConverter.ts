"use node";

import { internalAction } from "../_generated/server";
import { internal } from "../_generated/api";
import { v } from "convex/values";
import { generateText } from "ai";
import { google } from "@ai-sdk/google";

export const llmFriendlyConverter = internalAction({
  args: {
    documentId: v.id("documents"),
    source: v.union(
      v.object({
        type: v.literal("storage"),
        storageId: v.string(),
      }),
      v.object({
        type: v.literal("url"),
        url: v.string(),
      })
    ),
  },
  handler: async (ctx, args) => {
    const { documentId, source } = args;

    try {
      // 1. Get document buffer
      let documentBuffer: Buffer;
      if (source.type === "storage") {
        const blob = await ctx.storage.get(source.storageId);
        if (!blob) {
          throw new Error(`Storage object not found: ${source.storageId}`);
        }
        documentBuffer = Buffer.from(await blob.arrayBuffer());
      } else { // source.type === "url"
        const response = await fetch(source.url);
        if (!response.ok) {
          throw new Error(`Failed to fetch URL: ${source.url} (Status: ${response.status})`);
        }
        documentBuffer = Buffer.from(await response.arrayBuffer());
      }

      // 2. Convert document to Markdown using AI
      const { text } = await generateText({
        model: google("gemini-2.5-flash-preview-04-17"),
        messages: [
          {
            role: 'user',
            content: [
              { 
                type: 'text', 
                text: "Convert this document to clean, well-structured Markdown. Preserve the original structure, formatting, and content while making it readable and properly formatted for Markdown." 
              },
              { 
                type: 'image', 
                image: documentBuffer, 
                mimeType: 'application/pdf' 
              }
            ]
          }
        ]
      });

      // 3. Save the converted Markdown to the document
      await ctx.runMutation(internal.ai.llmFriendlyConverter.updateDocumentMarkdown, {
        documentId,
        markdown: text,
      });

      return null;
    } catch (error) {
      // Enhanced error logging for better debugging
      console.error("Error in llmFriendlyConverter:", {
        documentId,
        sourceType: source.type,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  },
});