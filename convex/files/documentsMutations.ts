import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { zCustomMutation } from 'convex-helpers/server/zod';
import { NoOp } from 'convex-helpers/server/customFunctions';
import {
  internalMutation
} from '../_generated/server';
import { ConvexError, v } from 'convex/values';

// Import all document schemas
import {
  CreateDocumentArgsSchema,
  UpdateDocumentAndChecklistArgsSchema,
  UpdateDocumentStatusArgsSchema,
  UpdateDocumentAiAnalysisArgsSchema,
  DocumentAnalysisSchema,
  SaveAnalysisResultsArgsSchema,
} from './documentsSchema';



// Import the main zMutation from functions.ts (with triggers)
import { zMutation } from '../functions';

/**
 * Simple wrapper around Convex's internalMutation function that uses Zod for validation.
 * Uses NoOp to avoid complex type recursion issues with triggers.
 */
const zInternalMutation = zCustomMutation(internalMutation, NoOp);

/**
 * createDocument mutation
 *
 * @description
 * Creates a new document record associated with a file.
 *
 * @example
 * const documentId = await convex.runMutation(api.files.documentsMutations.createDocument, {
 *   storageId: "kg4def456...",
 *   name: "Contract Analysis",
 *   aiAnalysis: "This document contains..."
 * });
 *
 * @throws {ZodError} If arguments are invalid.
 */
export const createDocument = zMutation({
  args: {
    storageId: zid('_storage'),
    name: z.string(),
    aiAnalysis: z.string(),
  },
  returns: zid('documents'),
  handler: async (ctx, args) => {
    const { storageId, name, aiAnalysis } = CreateDocumentArgsSchema.parse(args);

    // Create the document record
    const file = await ctx.db
      .query("files")
      .filter(q => q.eq(q.field("fileStorageId"), storageId))
      .first();

    if (!file) {
      throw new ConvexError({
        message: "File not found",
        code: "FILE_NOT_FOUND",
      });
    }

    const documentId = await ctx.db.insert('documents', {
      fileId: file._id,
      name,
      ai_analysis: aiAnalysis,
      status: 'completed',  // Set status to completed
      updated_at: Date.now()
    });

    return documentId;
  }
});

/**
 * internalCreateDocument mutation
 *
 * @description
 * Internal mutation to create a document with associated file and relationships.
 * Used by Box sync and other internal processes.
 *
 * @example
 * const documentId = await ctx.runMutation(internal.files.documentsMutations.internalCreateDocument, {
 *   clientId: "j57abc123...",
 *   storageId: "kg4def456...",
 *   fileName: "contract.pdf",
 *   fileExtension: "pdf",
 *   fileSize: 1024000,
 *   boxFileId: "box123..."
 * });
 *
 * @throws {ZodError} If arguments are invalid.
 */
export const internalCreateDocument = zInternalMutation({
  args: {
    clientId: zid("clients"),
    storageId: zid("_storage"),
    fileName: z.string(),
    fileExtension: z.string().optional(),
    fileSize: z.number().optional(),
    boxFileId: z.string().optional(),
  },
  returns: zid("documents").nullable(),
  handler: async (ctx, args) => {
    const { clientId, storageId, fileName, fileExtension, fileSize, boxFileId } = args;

    const fileId = await ctx.db.insert("files", {
      docType: "DOCUMENT",
      title: fileName,
      fileName: fileName,
      fileFilename: fileName, // Store the original filename
      fileStorageId: storageId,
      fileExtension: fileExtension,
      fileSize: fileSize,
      box_file_id: boxFileId, // Save the Box file ID
      updated_at: Date.now(),
    });

    const documentId = await ctx.db.insert("documents", {
      fileId,
      name: fileName,
      updated_at: Date.now(),
    });

    await ctx.db.insert("file_relationships", {
      file_id: fileId,
      subject_type: "client",
      // @ts-ignore
      subject_id: clientId,
      linked_at: Date.now(),
    });

    return documentId;
  },
});

/**
 * updateDocumentAndChecklist mutation
 *
 * @description
 * Updates document AI analysis and user document checklist based on analysis results.
 * Finds matching document definitions and updates checklist accordingly.
 *
 * @throws {ZodError} If arguments are invalid.
 */
export const updateDocumentAndChecklist = zMutation({
  args: {
    fileId: zid('files'),
    analysisResult: z.object({
      documentType: z.string(),
      summary: z.string(),
      analysis: z.string(),
      entities: z.array(z.string()).optional(),
      maritalStatus: z.string().optional(),
      spouses: z.array(z.object({
        name: z.string().optional(),
        dateOfBirth: z.string().optional(),
      })).optional(),
      children: z.array(z.object({
        name: z.string().optional(),
        dateOfBirth: z.string().optional(),
      })).optional(),
      assets: z.array(z.object({
        name: z.string().optional(),
        value: z.number().optional(),
        type: z.string().optional(),
        description: z.string().optional()
      })).optional()
    }),
    userId: zid('users')
  },
  returns: z.object({ success: z.boolean() }),
  handler: async (ctx, args) => {
    const { fileId, analysisResult, userId } = UpdateDocumentAndChecklistArgsSchema.parse(args);

    try {
      // 1. Get the file record
      const file = await ctx.db.get(fileId);
      if (!file) {
        throw new ConvexError({
          message: "File not found",
          code: "FILE_NOT_FOUND",
        });
      }

      // 2. Get the file's storage ID
      const storageId = file.fileStorageId;
      if (!storageId) {
        throw new ConvexError({
          message: "File storage ID not found",
          code: "FILE_STORAGE_ID_NOT_FOUND",
        });
      }

      // 3. Find the document record associated with this storage ID
      const document = await ctx.db
        .query("documents")
        .filter(q => q.eq(q.field("fileId"), file._id))
        .first();

      // 4. Update the document's ai_analysis field
      if (document) {
        await ctx.db.patch(document._id, {
          ai_analysis: analysisResult.analysis,
          status: 'completed',  // Set status to completed
          updated_at: Date.now()
        });
      } else {
      // Create a new document record if it doesn't exist
      await ctx.db.insert("documents", {
        fileId: file._id,
        name: file.title || "", // Use file.title here
        ai_analysis: analysisResult.analysis,
        status: 'completed',  // Set status to completed
        updated_at: Date.now()
        });
      }

      // 4. Find the document definition that matches the documentType
      const documentDefinition = await ctx.db
        .query("document_definitions")
        .filter(q => q.eq(q.field("name"), analysisResult.documentType))
        .first();

      // 5. If a matching document definition is found, update the checklist
      if (documentDefinition) {
        await ctx.db.insert("user_document_checklist", {
          document_definition_id: documentDefinition._id,
          user_id: userId,
          status: "completed",
          updated_at: Date.now()
        });
      }

      return { success: true };
    } catch (error) {
      // Handle errors
      if (error instanceof ConvexError) throw error;

      throw new ConvexError({
        message: error instanceof Error ? error.message : "Unknown error in updateDocumentAndChecklist",
        code: "UPDATE_DOCUMENT_ERROR",
      });
    }
  }
});

/**
 * updateDocumentStatus mutation
 *
 * @description
 * Updates a document's status field.
 *
 * @throws {ZodError} If arguments are invalid.
 */
export const updateDocumentStatus = zMutation({
  args: {
    documentId: zid('documents'),
    status: z.string()
  },
  returns: zid('documents'),
  handler: async (ctx, args) => {
    const { documentId, status } = UpdateDocumentStatusArgsSchema.parse(args);

    // Update the document record
    await ctx.db.patch(documentId, {
      status: status,
      updated_at: Date.now()
    });

    return documentId;
  }
});

/**
 * updateDocumentAiAnalysis mutation
 *
 * @description
 * Updates a document's AI analysis field.
 *
 * @throws {ZodError} If arguments are invalid.
 */
export const updateDocumentAiAnalysis = zMutation({
  args: {
    documentId: zid('documents'),
    aiAnalysis: z.string()
  },
  returns: zid('documents'),
  handler: async (ctx, args) => {
    const { documentId, aiAnalysis } = UpdateDocumentAiAnalysisArgsSchema.parse(args);

    // Update the document record
    await ctx.db.patch(documentId, {
      ai_analysis: aiAnalysis,
      status: 'completed',  // Set status to completed
      updated_at: Date.now()
    });

    return documentId;
  }
});

/**
 * saveAnalysisResults mutation
 *
 * @description
 * Saves document analysis results to the database with structured insights and metadata.
 *
 * @throws {ZodError} If arguments are invalid.
 */
export const saveAnalysisResults = zMutation({
  args: {
    documentId: zid("documents"),
    analysisResults: DocumentAnalysisSchema,
  },
  returns: zid("documents"),
  handler: async (ctx, args) => {
    const { documentId, analysisResults } = SaveAnalysisResultsArgsSchema.parse(args);
    
    // Check if document exists
    const document = await ctx.db.get(documentId);
    if (!document) {
      throw new ConvexError(`Document with ID ${documentId} not found`);
    }
    
    // Update document with analysis results
    await ctx.db.patch(documentId, {
      insights: {
        ...analysisResults.insights,
        sentiment: analysisResults.insights.sentiment ?? "neutral"
      },
      metadata: analysisResults.metadata,
      status: "analyzed",
      updated_at: Date.now(),
    });
    
    return documentId;
  },
});

/**
 * Simple internal mutation to update a document's markdown field
 */
export const updateDocumentMarkdown = internalMutation({
  args: {
    documentId: v.id("documents"),
    markdown: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.documentId, {
      markdown: args.markdown,
      updated_at: Date.now(),
    });
  },
});
