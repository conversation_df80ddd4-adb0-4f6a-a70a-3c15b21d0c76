import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// ========================================
// SHARED SCHEMAS & CONSTANTS
// ========================================

/**
 * Common person information schema (used for spouses, children, etc.)
 */
const PersonInfoSchema = z.object({
  name: z.string().optional(),
  dateOfBirth: z.string().optional(),
});

/**
 * Document types enum
 */
export const DocTypeSchema = z.enum([
  'KNOWLEDGE_BASE', 
  'MEETING_NOTES', 
  'CONTRACT', 
  'DOCUMENT', 
  'BILL'
]);

/**
 * Parent entity ID union - entities that can have documents attached
 */
export const ParentEntityIdSchema = z.union([
  zid('tasks'),
  zid('projects'),
  zid('decisions'),
  zid('organizations'),
  zid('users'),
  zid('people'),
  zid('bills')
]);

/**
 * Asset information schema
 */
const AssetSchema = z.object({
  name: z.string().optional(),
  value: z.number().optional(),
  type: z.string().optional(),
  description: z.string().optional(),
});

/**
 * Document analysis insights
 */
export const DocumentInsightsSchema = z.object({
  topics: z.array(z.string()),
  summary: z.string(),
  sentiment: z.enum(["positive", "neutral", "negative"]).optional(),
});

/**
 * Document metadata
 */
export const DocumentMetadataSchema = z.object({
  pageCount: z.number().optional(),
  wordCount: z.number().optional(),
  processingTime: z.number().optional(),
}).optional();

// ========================================
// DOCUMENT MUTATION SCHEMAS
// ========================================

/**
 * Schema for createDocument mutation arguments
 */
export const CreateDocumentArgsSchema = z.object({
  storageId: zid('_storage'),
  name: z.string(),
  aiAnalysis: z.string(),
});

/**
 * Schema for internalCreateDocument mutation arguments
 */
export const InternalCreateDocumentArgsSchema = z.object({
  clientId: zid("clients"),
  storageId: zid("_storage"),
  fileName: z.string(),
  fileExtension: z.string().optional(),
  fileSize: z.number().optional(),
  boxFileId: z.string().optional(),
});

/**
 * Schema for spouse information in document analysis
 * @deprecated Use PersonInfoSchema instead
 */
export const SpouseSchema = PersonInfoSchema;

/**
 * Schema for child information in document analysis
 * @deprecated Use PersonInfoSchema instead
 */
export const ChildSchema = PersonInfoSchema;

/**
 * Schema for document analysis result
 */
export const AnalysisResultSchema = z.object({
  documentType: z.string(),
  summary: z.string(),
  analysis: z.string(),
  entities: z.array(z.string()).optional(),
  maritalStatus: z.string().optional(),
  spouses: z.array(PersonInfoSchema).optional(),
  children: z.array(PersonInfoSchema).optional(),
  assets: z.array(AssetSchema).optional(),
});

/**
 * Schema for updateDocumentAndChecklist mutation arguments
 */
export const UpdateDocumentAndChecklistArgsSchema = z.object({
  fileId: zid('files'),
  analysisResult: AnalysisResultSchema,
  userId: zid('users'),
});

/**
 * Schema for updateUserDocumentChecklist mutation arguments
 */
export const UpdateUserDocumentChecklistArgsSchema = z.object({
  documentDefinitionId: z.string(),
  userId: z.string(),
  status: z.string(),
});

/**
 * Schema for updateDocumentStatus mutation arguments
 */
export const UpdateDocumentStatusArgsSchema = z.object({
  documentId: zid('documents'),
  status: z.string(),
});

/**
 * Schema for updateDocumentAiAnalysis mutation arguments
 */
export const UpdateDocumentAiAnalysisArgsSchema = z.object({
  documentId: zid('documents'),
  aiAnalysis: z.string(),
});

/**
 * Schema for document analysis results
 */
export const DocumentAnalysisSchema = z.object({
  insights: DocumentInsightsSchema,
  metadata: DocumentMetadataSchema,
});

/**
 * Schema for saveAnalysisResults mutation arguments
 */
export const SaveAnalysisResultsArgsSchema = z.object({
  documentId: zid("documents"),
  analysisResults: DocumentAnalysisSchema,
});

/**
 * Schema for saveMarkdown mutation arguments
 */
export const SaveMarkdownArgsSchema = z.object({
  documentId: zid("documents"),
  markdown: z.string(),
});

// ========================================
// DOCUMENT QUERY SCHEMAS
// ========================================

/**
 * Schema for getByParentEntity query arguments
 */
export const GetByParentEntityArgsSchema = z.object({
  parentEntityId: ParentEntityIdSchema,
  docType: DocTypeSchema.optional(),
});

/**
 * Schema for getDocumentDefinitions query arguments
 */
export const GetDocumentDefinitionsArgsSchema = z.object({
  category: z.string().optional(),
});

/**
 * Schema for getUserDocumentChecklist query arguments
 */
export const GetUserDocumentChecklistArgsSchema = z.object({
  userId: zid('users'),
});

/**
 * Schema for getDocumentByFileId query arguments
 */
export const GetDocumentByFileIdArgsSchema = z.object({
  fileId: zid('files'),
});

/**
 * Schema for getDocumentById query arguments
 */
export const GetDocumentByIdArgsSchema = z.object({
  documentId: zid('documents'),
});

/**
 * Schema for getSignRequestId query arguments
 */
export const GetSignRequestIdArgsSchema = z.object({
  documentId: zid('documents'),
});

/**
 * Schema for document type counts return value
 */
export const DocumentTypeCountsSchema = z.object({
  KNOWLEDGE_BASE: z.number(),
  MEETING_NOTES: z.number(),
  CONTRACT: z.number(),
  DOCUMENT: z.number(),
  BILL: z.number(),
});

/**
 * Schema for user document checklist entry with definition
 */
export const UserDocumentChecklistEntrySchema = z.object({
  _id: zid('user_document_checklist'),
  _creationTime: z.number(),
  document_definition_id: zid('document_definitions'),
  user_id: zid('users'),
  status: z.string(),
  updated_at: z.number(),
  definition: z.object({
    _id: zid('document_definitions'),
    _creationTime: z.number(),
    name: z.string(),
    category: z.string(),
    description: z.string().optional(),
    updated_at: z.number().optional(),
  }).optional(),
});

// ========================================
// TYPE EXPORTS
// ========================================

export type CreateDocumentArgs = z.infer<typeof CreateDocumentArgsSchema>;
export type InternalCreateDocumentArgs = z.infer<typeof InternalCreateDocumentArgsSchema>;
export type Spouse = z.infer<typeof SpouseSchema>;
export type Child = z.infer<typeof ChildSchema>;
export type Asset = z.infer<typeof AssetSchema>;
export type AnalysisResult = z.infer<typeof AnalysisResultSchema>;
export type UpdateDocumentAndChecklistArgs = z.infer<typeof UpdateDocumentAndChecklistArgsSchema>;
export type UpdateUserDocumentChecklistArgs = z.infer<typeof UpdateUserDocumentChecklistArgsSchema>;
export type UpdateDocumentStatusArgs = z.infer<typeof UpdateDocumentStatusArgsSchema>;
export type UpdateDocumentAiAnalysisArgs = z.infer<typeof UpdateDocumentAiAnalysisArgsSchema>;
export type DocumentInsights = z.infer<typeof DocumentInsightsSchema>;
export type DocumentMetadata = z.infer<typeof DocumentMetadataSchema>;
export type DocumentAnalysis = z.infer<typeof DocumentAnalysisSchema>;
export type SaveAnalysisResultsArgs = z.infer<typeof SaveAnalysisResultsArgsSchema>;
export type SaveMarkdownArgs = z.infer<typeof SaveMarkdownArgsSchema>;
export type DocType = z.infer<typeof DocTypeSchema>;
export type GetByParentEntityArgs = z.infer<typeof GetByParentEntityArgsSchema>;
export type GetDocumentDefinitionsArgs = z.infer<typeof GetDocumentDefinitionsArgsSchema>;
export type GetUserDocumentChecklistArgs = z.infer<typeof GetUserDocumentChecklistArgsSchema>;
export type GetDocumentByFileIdArgs = z.infer<typeof GetDocumentByFileIdArgsSchema>;
export type GetDocumentByIdArgs = z.infer<typeof GetDocumentByIdArgsSchema>;
export type GetSignRequestIdArgs = z.infer<typeof GetSignRequestIdArgsSchema>;
export type DocumentTypeCounts = z.infer<typeof DocumentTypeCountsSchema>;
export type UserDocumentChecklistEntry = z.infer<typeof UserDocumentChecklistEntrySchema>;